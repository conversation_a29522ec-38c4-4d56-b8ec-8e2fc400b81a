import json
import secrets
from typing import Optional, Set, Dict, Any

import aiomysql
from fastapi import HTTPException

from app.core.logging import logger
from app.models import APIKeyData

class DBManager:
    """
    管理与MySQL数据库的连接和操作。
    """
    _pool: Optional[aiomysql.Pool] = None
    valid_keys_cache: Set[str] = set()

    @classmethod
    async def connect(cls, db_config: Dict[str, Any]):
        """
        创建并初始化数据库连接池。
        """
        try:
            cls._pool = await aiomysql.create_pool(
                host=db_config['host'],
                port=db_config.get('port', 3306),
                user=db_config['user'],
                password=db_config['password'],
                db=db_config['db'] or os.getenv("DB_NAME"),
                autocommit=True,
                charset='utf8mb4',
                cursorclass=aiomysql.DictCursor
            )
            logger.info(f"成功连接到MySQL数据库: {db_config['host']}/{db_config['db']}")
            await cls._create_tables_if_not_exists()
            await cls.refresh_keys_cache()
        except Exception as e:
            logger.error(f"无法连接到MySQL数据库: {e}", exc_info=True)
            raise

    @classmethod
    async def close(cls):
        """
        关闭数据库连接池。
        """
        if cls._pool:
            cls._pool.close()
            await cls._pool.wait_closed()
            logger.info("数据库连接池已关闭。")

    @classmethod
    async def _create_tables_if_not_exists(cls):
        """
        如果表不存在，则创建它们。
        """
        async with cls._pool.acquire() as conn:
            async with conn.cursor() as cur:
                # 创建 api_keys 表
                await cur.execute("""
                CREATE TABLE IF NOT EXISTS `api_keys` (
                  `api_key` VARCHAR(100) NOT NULL PRIMARY KEY,
                  `owner` VARCHAR(100) NOT NULL,
                  `total_requests` BIGINT UNSIGNED NOT NULL DEFAULT 0,
                  `total_data` BIGINT UNSIGNED NOT NULL DEFAULT 0,
                  `ips` JSON,
                  `locked_until` BIGINT NOT NULL DEFAULT 0,
                  `last_request_at` TIMESTAMP NULL,
                  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  INDEX `idx_owner` (`owner`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                """)
                logger.info("数据库表 'api_keys' 已确认存在。")

                # 创建 request_logs 表
                await cur.execute("""
                CREATE TABLE IF NOT EXISTS `request_logs` (
                  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
                  `api_key` VARCHAR(100) NOT NULL,
                  `timestamp` TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                  INDEX `idx_api_key_timestamp` (`api_key`, `timestamp`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                """)
                logger.info("数据库表 'request_logs' 已确认存在。")

    @classmethod
    async def refresh_keys_cache(cls):
        """
        从数据库刷新有效的API密钥缓存。
        """
        try:
            async with cls._pool.acquire() as conn:
                async with conn.cursor() as cur:
                    await cur.execute("SELECT api_key FROM api_keys;")
                    rows = await cur.fetchall()
                    keys = {row['api_key'] for row in rows}
                    
                    added = len(keys - cls.valid_keys_cache)
                    removed = len(cls.valid_keys_cache - keys)
                    
                    if added > 0 or removed > 0:
                        logger.info(f"API密钥缓存已刷新: 总数={len(keys)}, 新增={added}, 移除={removed}")
                    
                    cls.valid_keys_cache = keys
        except Exception as e:
            logger.error(f"刷新API密钥缓存失败: {e}", exc_info=True)

    @classmethod
    async def get_key_data(cls, api_key: str) -> Optional[APIKeyData]:
        """
        根据API密钥从数据库检索密钥数据。
        """
        async with cls._pool.acquire() as conn:
            async with conn.cursor() as cur:
                await cur.execute("SELECT * FROM api_keys WHERE api_key = %s;", (api_key,))
                data = await cur.fetchone()
                if data:
                    # aiomysql/mysql-connector-python might return JSON as string
                    if data.get('ips') and isinstance(data['ips'], str):
                        try:
                            data['ips'] = json.loads(data['ips'])
                        except json.JSONDecodeError:
                            data['ips'] = {} # Default to empty dict on error
                    return APIKeyData(**data)
                return None

    @classmethod
    async def update_key_stats(cls, key_data: APIKeyData):
        """
        更新API密钥的使用统计信息。
        """
        ips_json = json.dumps(key_data.ips)
        async with cls._pool.acquire() as conn:
            async with conn.cursor() as cur:
                await cur.execute(
                    "UPDATE api_keys SET total_requests = %s, total_data = %s, last_request_at = %s, ips = %s WHERE api_key = %s;",
                    (key_data.total_requests, key_data.total_data, key_data.last_request_at, ips_json, key_data.api_key)
                )

    @classmethod
    async def update_lock_status(cls, api_key: str, locked_until: int):
        """
        更新API密钥的锁定状态。
        """
        async with cls._pool.acquire() as conn:
            async with conn.cursor() as cur:
                await cur.execute("UPDATE api_keys SET locked_until = %s WHERE api_key = %s;", (locked_until, api_key))

    @classmethod
    async def create_api_key(cls, owner: str) -> str:
        """
        创建一个新的API密钥并存入数据库。
        """
        new_key = f"sk-{secrets.token_urlsafe(36)}"
        try:
            async with cls._pool.acquire() as conn:
                async with conn.cursor() as cur:
                    await cur.execute("INSERT INTO api_keys (api_key, owner) VALUES (%s, %s);", (new_key, owner))
            
            cls.valid_keys_cache.add(new_key)
            logger.info(f"成功创建并存储新的API密钥，所有者: {owner}")
            return new_key
        except Exception as e:
            logger.error(f"创建API密钥失败: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail="数据库操作失败，无法创建密钥。")

# 单例实例
db_manager = DBManager()