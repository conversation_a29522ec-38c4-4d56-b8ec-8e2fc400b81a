import time
from datetime import datetime, timedelta
from typing import Dict, Any, Tu<PERSON>

from app.db.database import db_manager
from app.models import APIKeyData

class RateLimiter:
    """
    处理API请求的速率和资源限制。
    使用 MySQL 数据库来实现速率限制。
    """
    def is_locked(self, key_data: APIKeyData) -> bool:
        """
        检查API密钥是否因异常活动而被锁定。
        """
        return key_data.locked_until > time.time()

    async def check_ip_limit(self, key_data: APIKeyData, ip: str, config: Dict[str, Any]) -> bool:
        """
        检查在给定时间窗口内使用API密钥的唯一IP地址数量。
        如果超过限制，则锁定密钥。
        """
        now = time.time()
        hours = config['ip_lock_hours']
        max_ips = config['ip_lock_max_ips']
        
        ip_records = key_data.ips or {}
        
        cutoff_timestamp = now - (hours * 3600)
        
        valid_ips = {
            r_ip for r_ip, ts_str in ip_records.items()
            if datetime.fromisoformat(ts_str.split('_')[0]).timestamp() > cutoff_timestamp
        }
        
        valid_ips.add(ip)
        
        if len(valid_ips) > max_ips:
            lock_until = int(now + (hours * 3600))
            await db_manager.update_lock_status(key_data.api_key, lock_until)
            return False
            
        return True

    async def check_rate_limit(self, api_key: str, request_size: int, key_data: APIKeyData, config: Dict[str, Any]) -> Tuple[bool, str]:
        """
        使用 MySQL 检查每日请求次数、数据量和每分钟请求数（RPM）限制。
        """
        # 1. 检查每日请求总数
        if key_data.total_requests >= config['daily_requests_limit']:
            return False, "每日请求次数超限"
            
        # 2. 检查每日数据总量
        if key_data.total_data + request_size > config['daily_data_limit_mb'] * 1024 * 1024:
            return False, "每日数据量超限"
            
        # 3. 使用 MySQL 检查RPM（每分钟请求数）
        rpm_limit = config['rpm_limit']
        if rpm_limit <= 0:
            return True, ""

        async with db_manager._pool.acquire() as conn:
            async with conn.cursor() as cur:
                # 插入当前请求记录
                await cur.execute("INSERT INTO request_logs (api_key) VALUES (%s);", (api_key,))
                
                # 计算一分钟前的时间点
                one_minute_ago = datetime.utcnow() - timedelta(seconds=60)
                
                # 计算窗口内的请求数
                await cur.execute(
                    "SELECT COUNT(*) as count FROM request_logs WHERE api_key = %s AND timestamp > %s;",
                    (api_key, one_minute_ago)
                )
                result = await cur.fetchone()
                current_requests = result['count']

        if current_requests > rpm_limit:
            return False, f"请求频率过快 (RPM limit: {rpm_limit})"
            
        return True, ""

# 单例实例
rate_limiter = RateLimiter()